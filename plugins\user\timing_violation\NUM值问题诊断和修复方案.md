# NUM值问题诊断和修复方案

## 问题描述

用户确认vio_summary.log文件中的NUM值是从1开始的，但在插件界面中显示的NUM值是从880887开始。

## 可能的原因

### 1. 数据库中有旧数据残留
- 之前的测试或导入留下了大NUM值的记录
- 新数据追加到了旧数据之后

### 2. 多次导入同一文件
- 虽然有重复检查逻辑，但可能存在边界情况
- 不同的case_name或corner导致重复导入

### 3. 测试数据干扰
- 可能之前运行了测试数据生成器，生成了大量测试数据
- 测试数据的NUM值可能很大

### 4. 数据库查询排序问题
- 虽然使用了`ORDER BY v.num`，但可能存在其他影响因素

## 诊断步骤

### 步骤1: 检查数据库内容
运行以下脚本检查数据库中的实际NUM值：

```bash
cd e:\doc\python\runsim_bak\plugins\user\timing_violation
python simple_db_check.py
```

### 步骤2: 检查vio_summary.log文件
确认原始文件的前几行：
```
NUM : 1
Hier : tb.writeback_unit.tlb[29].branch_pred[6].execute_unit
Time : 1523423 FS
Check : Setup violation on signal clk
------------------------------------------------------------
NUM : 2
...
```

### 步骤3: 检查数据库记录数
如果数据库中有大量记录（比如几十万条），可能包含了测试数据。

## 修复方案

### 方案1: 清理数据库重新导入（推荐）

1. **备份当前数据库**：
   ```bash
   copy "VIOLATION_CHECK\timing_violations.db" "VIOLATION_CHECK\timing_violations_backup.db"
   ```

2. **运行修复脚本**：
   ```bash
   cd e:\doc\python\runsim_bak\plugins\user\timing_violation
   python fix_num_values.py
   ```

3. **验证结果**：
   - 重新启动插件
   - 检查NUM列是否从1开始显示

### 方案2: 手动清理数据库

如果自动修复脚本无法运行，可以手动操作：

1. **关闭插件**
2. **删除数据库文件**：
   ```bash
   del "VIOLATION_CHECK\timing_violations.db"
   ```
3. **重新启动插件并导入数据**

### 方案3: SQL直接修复

如果只是NUM值偏移问题，可以直接修改：

```sql
-- 假设当前最小NUM值是880887，需要调整为从1开始
UPDATE timing_violations 
SET num = num - 880886 
WHERE num >= 880887;
```

## 预防措施

### 1. 清理测试数据
- 删除所有测试生成的vio_summary.log文件
- 确保只导入真实的仿真结果

### 2. 检查导入逻辑
- 确认每次导入前数据库状态
- 避免重复导入同一文件

### 3. 数据验证
- 导入后检查NUM值范围
- 确认第一条记录的NUM值为1

## 验证修复结果

修复后，应该看到：
- **第1页**: NUM列显示 1, 2, 3, 4, 5, ...
- **第2页**: NUM列显示 101, 102, 103, ... (假设每页100条)
- **数据库查询**: `SELECT MIN(num) FROM timing_violations` 返回 1

## 如果问题仍然存在

如果按照上述方案修复后问题仍然存在，请检查：

1. **vio_summary.log文件内容**：
   - 确认第一行确实是 `NUM : 1`
   - 检查是否有文件编码问题

2. **解析器逻辑**：
   - 检查parser.py中的NUM解析逻辑
   - 确认没有额外的偏移计算

3. **数据模型查询**：
   - 检查models.py中的查询逻辑
   - 确认排序和过滤条件

## 联系支持

如果以上方案都无法解决问题，请提供：
1. vio_summary.log文件的前10行内容
2. 数据库检查脚本的输出结果
3. 插件的错误日志（如果有）

这将帮助进一步诊断问题的根本原因。

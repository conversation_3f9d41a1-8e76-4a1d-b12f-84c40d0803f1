# NUM列分页显示修复说明

## 问题描述

在时序违例插件的高性能模式中，NUM列显示存在问题：
- **第一页**：NUM列显示数据库中的原始值（如760887、760888等），而不是从1开始的页面序号
- **第二页及后续页面**：NUM列显示正常，从1开始计数

## 问题分析

### 根本原因
在高性能表格的`create_cell_label`方法中，NUM列使用了数据库中的原始`num`字段值，而不是计算页面内的相对序号。

### 代码问题位置
**文件**: `main_window.py`
**方法**: `create_cell_label` (第847-851行)

**原始错误代码**:
```python
if col == 0:  # NUM
    num_value = violation.get('num', '')
    if num_value is None or num_value == '':
        num_value = violation.get('id', '')
    text = str(num_value) if num_value else ''
```

### 为什么第二页显示正常
在代码中存在另一个方法`_create_row_from_cached_data`（第1394行），它正确地计算了页面内序号：
```python
str(self.current_page * self.page_size + row_index + 1)  # NUM
```

这说明缓存数据的渲染路径是正确的，但主要的渲染路径有问题。

## 修复方案

### 修复1: create_cell_label方法
**位置**: `main_window.py` 第847-851行

**修复后代码**:
```python
if col == 0:  # NUM
    # 计算页面内的序号：当前页 * 页面大小 + 行索引 + 1
    # 这里的row是相对于当前页的行索引，需要转换为页面内的显示序号
    page_num = row + 1  # row是从0开始的相对行索引，转换为从1开始的显示序号
    text = str(page_num)
```

**修复逻辑**:
- `row`参数是相对于当前页的行索引（0-based）
- 直接使用`row + 1`得到页面内的显示序号（1-based）
- 每页都从1开始计数

### 修复2: _get_display_data方法
**位置**: `main_window.py` 第159-178行

**修复内容**:
1. 修改方法签名，添加`row_index`参数
2. 在`data`方法中传递行索引
3. 计算页面内的相对序号

**修复后代码**:
```python
def _get_display_data(self, violation, column, row_index=None):
    """获取显示数据"""
    if column == 0:  # NUM
        # 计算页面内的序号
        if row_index is not None and self._parent_window:
            try:
                # 获取当前页面信息
                current_page = getattr(self._parent_window, 'current_page', 0)
                page_size = getattr(self._parent_window, 'page_size', 100)
                # 计算在当前页面中的相对位置
                page_row = row_index % page_size
                return str(page_row + 1)
            except:
                pass
        
        # 回退逻辑：使用数据库中的num字段
        num_value = violation.get('num', '')
        if num_value is None or num_value == '':
            num_value = violation.get('id', '')
        return str(num_value) if num_value else ''
```

## 修复效果

### 修复前
- **第1页**: NUM列显示 760887, 760888, 760889, ...
- **第2页**: NUM列显示 1, 2, 3, ...（正常）

### 修复后
- **第1页**: NUM列显示 1, 2, 3, ...
- **第2页**: NUM列显示 1, 2, 3, ...
- **所有页面**: 都从1开始计数，保持一致性

## 技术细节

### 页面内序号计算逻辑
```python
# 对于create_cell_label方法
page_num = row + 1  # row是页面内的相对行索引(0-based)

# 对于_get_display_data方法
page_row = row_index % page_size  # 将绝对行索引转换为页面内索引
display_num = page_row + 1        # 转换为1-based显示
```

### 数据流说明
1. **高性能模式主要路径**: `create_cell_label` → 直接计算页面内序号
2. **标准模式/回退路径**: `_get_display_data` → 通过模运算计算页面内序号
3. **缓存数据路径**: `_create_row_from_cached_data` → 已经正确实现

## 测试验证

### 测试场景
1. **第1页显示**: 确认NUM列从1开始
2. **页面切换**: 确认每页都从1开始
3. **大数据集**: 确认在大数据集下的表现
4. **边界情况**: 确认最后一页的显示

### 预期结果
- 所有页面的NUM列都从1开始计数
- 页面间显示一致
- 不再显示数据库中的原始num值
- 用户体验更加直观

## 兼容性说明

- ✅ 保持与现有功能的兼容性
- ✅ 不影响数据库存储
- ✅ 不影响其他列的显示
- ✅ 保持分页功能正常

## 相关文件

- `plugins/user/timing_violation/main_window.py` - 主要修复文件
- `plugins/user/timing_violation/test_num_column_fix.py` - 测试脚本
- `plugins/user/timing_violation/NUM列分页显示修复说明.md` - 本文档

## 总结

通过修复NUM列的计算逻辑，现在高性能模式的表格在所有页面都会显示一致的页面内序号（从1开始），提供了更好的用户体验和直观的数据浏览方式。

# NUM列显示问题修复说明

## 问题描述

在时序违例插件的高性能模式表格中，出现了以下问题：
1. NUM列没有显示内容
2. 终端报错：`设置工具提示失败: local variable 'index' referenced before assignment`

## 问题分析

### 问题1：工具提示错误
**原因**：在`create_cell_label`方法中，`index`变量只在`else`分支（第873行）中定义，但在第924行设置工具提示时无条件使用了`index`变量。当列号是0-6时，不会进入`else`分支，导致`index`变量未定义。

**错误代码**：
```python
# 安全地设置工具提示
try:
    tooltip = self.model.data(index, Qt.ToolTipRole)  # index未定义
    if tooltip:
        label.setToolTip(str(tooltip))
except Exception as e:
    print(f"设置工具提示失败: {e}")
```

### 问题2：NUM列显示为空
**原因**：可能的原因包括：
1. 数据库中`num`字段为空或None
2. `create_cell_label`方法返回None导致标签未添加到布局
3. 数据结构问题

## 修复方案

### 修复1：工具提示错误
**位置**：`main_window.py` 第922-940行

**修复方法**：重写工具提示设置逻辑，针对不同列使用不同的数据源：

```python
# 安全地设置工具提示
try:
    # 为特定列设置工具提示
    if col == 1:  # 层级路径
        tooltip = violation.get('hier', '')
        if tooltip:
            label.setToolTip(str(tooltip))
    elif col == 3:  # 检查信息
        tooltip = violation.get('check_info', '')
        if tooltip:
            label.setToolTip(str(tooltip))
    else:
        # 对于其他列，如果有index变量才使用model.data
        if 'index' in locals():
            tooltip = self.model.data(index, Qt.ToolTipRole)
            if tooltip:
                label.setToolTip(str(tooltip))
except Exception as e:
    print(f"设置工具提示失败: {e}")
```

### 修复2：NUM列显示问题
**位置1**：`main_window.py` 第161-166行（数据模型）
**位置2**：`main_window.py` 第847-855行（高性能表格）

**修复方法**：增强NUM列数据获取逻辑，提供回退机制：

```python
if col == 0:  # NUM
    num_value = violation.get('num', '')
    if num_value is None or num_value == '':
        # 如果数据库中没有num字段，尝试使用id或者生成序号
        num_value = violation.get('id', '')
    text = str(num_value) if num_value else ''
    # 调试信息
    if not text:
        print(f"警告: NUM列为空，violation keys: {list(violation.keys())}, num={violation.get('num')}, id={violation.get('id')}")
```

### 修复3：添加调试信息
**位置**：`main_window.py` 第783-788行

**修复方法**：添加标签创建失败的警告信息：

```python
else:
    label = self.create_cell_label(relative_row, col, violation, column_widths[col])
    if label:
        layout.addWidget(label)
    else:
        print(f"警告: 第{col}列标签创建失败，行{relative_row}")
```

## 修复效果

1. **消除错误信息**：不再出现`index`变量未定义的错误
2. **改善NUM列显示**：
   - 优先使用数据库中的`num`字段
   - 如果`num`字段为空，回退使用`id`字段
   - 添加调试信息帮助诊断问题
3. **增强调试能力**：添加了详细的调试信息，便于后续问题排查

## 测试建议

1. **启动插件**：检查是否还有`index`相关的错误信息
2. **加载数据**：观察NUM列是否正常显示
3. **查看调试信息**：如果NUM列仍为空，查看控制台的调试信息
4. **工具提示测试**：鼠标悬停在层级路径和检查信息列上，确认工具提示正常显示

## 后续优化建议

1. **数据一致性检查**：确保数据库中的`num`字段正确填充
2. **错误处理完善**：为所有可能的异常情况添加适当的处理
3. **性能优化**：减少不必要的调试信息输出
4. **用户体验**：考虑在NUM列为空时显示行号作为替代

## 相关文件

- `plugins/user/timing_violation/main_window.py` - 主要修复文件
- `plugins/user/timing_violation/debug_num_column.py` - 调试脚本
- `plugins/user/timing_violation/NUM列显示问题修复说明.md` - 本文档

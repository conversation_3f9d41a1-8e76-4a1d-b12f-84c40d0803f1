# NUM列设计说明

## 问题回顾

用户提出了一个非常重要的问题：**"在解析vio_summary.log中，就有NUM值，为啥还要分页计算，直接从已解析好的NUM值获取就行了吧，搞得这么复杂是什么考量？"**

## 用户说得完全正确！

经过重新分析，我发现之前的"修复"实际上是一个**设计错误**。正确的做法确实应该是直接使用解析好的NUM值。

## 原始设计的正确性

### 1. vio_summary.log格式
```
NUM : 760887
Hier : tb.writeback_unit.tlb[29].branch_pred[6].execute_unit
Time : 1523423 FS
Check : Setup violation on signal clk
------------------------------------------------------------
NUM : 760888
Hier : tb.cache_unit[24].pcie_if.cache_unit.pcie_if[2].execute
Time : 1523424 FS
Check : Hold violation on signal data[15]
------------------------------------------------------------
```

### 2. 解析器正确处理
```python
# parser.py 第200-204行
try:
    processed['NUM'] = int(violation['NUM'])
except ValueError:
    processed['NUM'] = 0
```

### 3. 数据库正确存储
```sql
CREATE TABLE timing_violations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    num INTEGER NOT NULL,  -- 存储原始的NUM值
    hier TEXT NOT NULL,
    ...
)
```

## 错误的"修复"

之前我将NUM列改为显示页面内序号（1, 2, 3...），这实际上：
1. **丢失了原始信息**：用户无法看到违例的真实编号
2. **破坏了数据完整性**：NUM列应该反映vio_summary.log中的原始编号
3. **增加了复杂性**：不必要的分页计算逻辑

## 正确的实现

### 简单直接的方案
```python
if col == 0:  # NUM
    # 直接使用解析好的NUM值，这是vio_summary.log中的原始违例编号
    num_value = violation.get('num', '')
    if num_value is None or num_value == '':
        # 如果数据库中没有num字段，尝试使用id作为回退
        num_value = violation.get('id', '')
    text = str(num_value) if num_value else ''
```

## 为什么原始NUM值更有意义

### 1. 保持数据完整性
- NUM值是vio_summary.log中的原始违例编号
- 这个编号在整个仿真过程中是唯一的
- 用户可以通过这个编号在原始日志中定位违例

### 2. 便于问题追踪
- 当用户报告问题时，可以直接引用NUM编号
- 便于在不同工具间交叉引用
- 保持与原始仿真工具的一致性

### 3. 避免混淆
- 页面内序号（1, 2, 3...）没有实际意义
- 可能导致用户误解数据的真实性
- 增加了不必要的复杂性

## 分页显示的正确理解

分页的目的是：
- **性能优化**：避免一次性渲染大量数据
- **用户体验**：便于浏览和导航

分页**不应该**改变数据的原始含义，NUM列应该始终显示原始的违例编号。

## 其他列的对比

| 列名 | 数据来源 | 是否需要分页计算 |
|------|----------|------------------|
| NUM | vio_summary.log原始编号 | ❌ 直接使用 |
| 层级路径 | vio_summary.log原始路径 | ❌ 直接使用 |
| 时间 | vio_summary.log原始时间 | ❌ 直接使用 |
| 检查信息 | vio_summary.log原始信息 | ❌ 直接使用 |
| 状态 | 数据库确认状态 | ❌ 直接使用 |

可以看出，**所有列都应该直接使用原始数据**，没有任何列需要分页计算。

## 用户体验考虑

### 如果用户需要页面内序号
如果确实需要页面内的相对序号，正确的做法是：
1. **添加新列**："页面序号"或"行号"
2. **保留NUM列**：继续显示原始编号
3. **让用户选择**：是否显示页面序号列

### 当前的正确显示
- **第1页**：NUM列显示 760887, 760888, 760889, ...（原始编号）
- **第2页**：NUM列显示 760987, 760988, 760989, ...（原始编号）
- **用户价值**：可以直接在原始日志中定位这些违例

## 总结

用户的质疑是完全正确的：
1. ✅ **直接使用解析好的NUM值**
2. ❌ **不需要分页计算**
3. ✅ **保持数据的原始性和完整性**
4. ✅ **简化代码逻辑**

这是一个很好的提醒：**简单直接的方案往往是最正确的方案**。过度设计和不必要的复杂性只会带来问题。

## 修复后的代码

现在NUM列的实现回到了最简单、最正确的方式：
```python
# 直接使用解析好的NUM值
num_value = violation.get('num', '')
text = str(num_value) if num_value else ''
```

这样既简单又正确，完全符合用户的期望和数据的原始含义。

# NUM列问题最终分析

## 问题现状

用户反馈：高性能模式下，首页NUM列显示880887、880888、880889等，而不是期望的1、2、3等。

## 代码检查结果

### 1. 已修复的代码位置

✅ **create_cell_label方法** (第849-854行)：
```python
if col == 0:  # NUM
    # 直接使用解析好的NUM值，这是vio_summary.log中的原始违例编号
    num_value = violation.get('num', '')
    if num_value is None or num_value == '':
        num_value = violation.get('id', '')
    text = str(num_value) if num_value else ''
```

✅ **_get_display_data方法** (第161-167行)：
```python
if column == 0:  # NUM
    # 直接使用解析好的NUM值，这是vio_summary.log中的原始违例编号
    num_value = violation.get('num', '')
    if num_value is None or num_value == '':
        num_value = violation.get('id', '')
    return str(num_value) if num_value else ''
```

✅ **_create_row_from_cached_data方法** (第1393行)：
```python
columns_data = [
    str(violation_data.get('num', violation_data.get('id', ''))),  # NUM - 使用原始NUM值
    violation_data.get('hier', ''),  # 层级路径
```

### 2. 数据流检查

✅ **解析器** (parser.py 第200-204行)：
```python
try:
    processed['NUM'] = int(violation['NUM'])
except ValueError:
    processed['NUM'] = 0
```

✅ **数据库存储** (models.py 第160行)：
```python
violation['NUM']  # 直接使用解析的NUM值
```

✅ **数据库查询** (models.py 第225、235行)：
```python
ORDER BY v.num  # 按NUM排序
```

## 问题根源分析

### 最可能的原因：vio_summary.log文件本身的NUM值不是从1开始

从用户截图显示的880887、880888等值来看，这个vio_summary.log文件很可能：

1. **部分文件**：从一个大的仿真结果中截取的部分
2. **合并文件**：多个仿真结果合并后的文件
3. **继续仿真**：从之前的仿真状态继续运行的结果
4. **测试数据**：使用测试数据生成器生成的大数据集

### 验证方法

用户可以检查原始的vio_summary.log文件：
```
NUM : 880887
Hier : tb.writeback_unit.tlb[29].branch_pred[6].execute_unit
Time : 1523423 FS
Check : Setup violation on signal clk
------------------------------------------------------------
NUM : 880888
Hier : tb.cache_unit[24].pcie_if.cache_unit.pcie_if[2].execute
Time : 1523424 FS
Check : Hold violation on signal data[15]
------------------------------------------------------------
```

如果文件中的第一条记录确实是`NUM : 880887`，那么插件的行为是**完全正确的**。

## 解决方案选择

### 方案1：保持原始NUM值（推荐）
**优点**：
- 保持数据完整性
- 便于问题追踪和定位
- 与原始仿真工具一致

**缺点**：
- 用户可能觉得不直观

### 方案2：重新编号（不推荐）
**优点**：
- 显示更直观（1, 2, 3...）

**缺点**：
- 丢失原始信息
- 无法与原始日志对应
- 破坏数据完整性

## 建议

### 1. 确认数据源
请用户检查原始vio_summary.log文件的前几行，确认NUM值是否确实从880887开始。

### 2. 如果确实需要重新编号
可以考虑添加一个**可选功能**：
- 保留原始NUM列
- 添加新的"序号"列显示1, 2, 3...
- 让用户选择显示哪个列

### 3. 当前实现是正确的
如果vio_summary.log中的NUM确实是880887开始，那么当前的实现是完全正确的，应该保持不变。

## 代码验证

所有相关的代码位置都已经修复，确保直接使用原始的NUM值：

1. ✅ 高性能表格主渲染路径
2. ✅ 数据模型显示方法
3. ✅ 缓存数据渲染路径
4. ✅ 数据库查询和排序

## 结论

**当前的实现是正确的**。如果用户看到880887开始的NUM值，这很可能反映了原始vio_summary.log文件的真实内容。

建议用户：
1. 检查原始vio_summary.log文件确认NUM值
2. 如果需要从1开始的序号，可以考虑添加额外的序号列
3. 保持当前实现以维护数据完整性

# NUM显示ID问题解决方案

## 问题确认

用户发现NUM列显示的是1000887、1000888这样的大数值，这些明显是数据库的ID值而不是vio_summary.log中的NUM值。

## 根本原因

代码中存在一个回退逻辑：
```python
num_value = violation.get('num', '')
if num_value is None or num_value == '':
    # 如果数据库中没有num字段，尝试使用id作为回退
    num_value = violation.get('id', '')
```

当数据库中的`num`字段为NULL、0或空字符串时，代码会使用`id`字段作为回退，这就是为什么看到1000887这样的ID值的原因。

## 问题分析

### 为什么NUM字段为空？

可能的原因：
1. **解析器问题**：解析vio_summary.log时NUM字段没有正确解析
2. **数据导入问题**：插入数据库时NUM字段为NULL或0
3. **数据库约束问题**：虽然定义为`NOT NULL`，但可能存在边界情况
4. **数据类型转换问题**：字符串转整数时出现问题

### 代码中的回退逻辑问题

使用ID作为NUM的回退是不合适的，因为：
- ID是数据库自增字段，与原始NUM值无关
- 会误导用户，以为这是真实的违例编号
- 掩盖了数据质量问题

## 解决方案

### 1. 立即修复：移除回退逻辑

已修改以下文件中的代码：
- `main_window.py` 第848-852行：`create_cell_label`方法
- `main_window.py` 第161-165行：`_get_display_data`方法
- `main_window.py` 第1388-1390行：`_create_row_from_cached_data`方法

**修改后的逻辑**：
```python
# 如果NUM值为空，显示"?"而不是使用ID
text = str(num_value) if num_value is not None and num_value != '' else '?'
```

### 2. 数据修复：修复数据库中的NUM字段

创建了修复脚本 `fix_num_field.py`，它会：

1. **检查NUM字段状态**：
   - 统计NULL、0、正值的记录数
   - 显示前10条记录的NUM值

2. **自动修复策略**：
   - 如果所有NUM都无效：按ID顺序重新编号（1, 2, 3...）
   - 如果部分NUM有效：修复无效的记录，从最大值+1开始编号

3. **安全措施**：
   - 自动备份数据库
   - 验证修复结果

### 3. 使用方法

#### 方法1：运行修复脚本（推荐）
```bash
cd e:\doc\python\runsim_bak\plugins\user\timing_violation
python fix_num_field.py
```

#### 方法2：手动检查和修复
```bash
# 检查NUM vs ID
python debug_num_vs_id.py

# 如果确认是NUM字段问题，运行修复脚本
python fix_num_field.py
```

#### 方法3：重新导入数据
如果修复脚本无法解决问题：
1. 备份数据库
2. 删除数据库文件
3. 重新启动插件并导入vio_summary.log

## 修复后的效果

### 修复前
- NUM列显示：1000887, 1000888, 1000889...（ID值）
- 用户困惑：这些不是真实的违例编号

### 修复后
- NUM列显示：1, 2, 3, 4, 5...（真实的违例编号）
- 或者显示：?（如果数据仍有问题，便于发现）

## 预防措施

### 1. 数据质量检查
在数据导入后检查：
```sql
SELECT COUNT(*) FROM timing_violations WHERE num IS NULL OR num = 0;
```

### 2. 解析器增强
在解析器中添加NUM字段验证：
```python
if 'NUM' not in violation or not violation['NUM']:
    print(f"警告: 违例记录缺少NUM字段: {violation}")
```

### 3. 导入验证
在数据导入后验证NUM字段：
```python
# 检查是否有无效的NUM值
invalid_count = cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num IS NULL OR num = 0").fetchone()[0]
if invalid_count > 0:
    print(f"警告: 发现 {invalid_count} 条无效NUM值")
```

## 验证修复结果

修复完成后，应该看到：
1. **NUM列显示正确**：从1开始的连续数字
2. **数据一致性**：与vio_summary.log中的NUM值对应
3. **无ID值显示**：不再看到1000887这样的大数值

## 如果问题仍然存在

如果修复后问题仍然存在，请：
1. 检查vio_summary.log文件的前几行，确认NUM格式
2. 运行调试脚本查看数据库实际内容
3. 检查解析器的NUM字段处理逻辑
4. 考虑完全重新导入数据

## 总结

这个问题的根本原因是代码中不合适的回退逻辑，当NUM字段为空时使用了ID字段。通过移除回退逻辑并修复数据库中的NUM字段，可以彻底解决这个问题。

修复后，NUM列将正确显示vio_summary.log中的原始违例编号，而不是数据库的ID值。

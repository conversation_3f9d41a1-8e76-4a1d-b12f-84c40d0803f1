#!/usr/bin/env python3
"""
检查数据库中NUM值的脚本
"""

import sqlite3
import os
import sys

def check_database():
    """检查数据库中的NUM值"""
    
    # 数据库路径
    db_path = os.path.join(os.getcwd(), "VIOLATION_CHECK", "timing_violations.db")
    print(f"检查数据库: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='timing_violations'")
        if not cursor.fetchone():
            print("❌ timing_violations表不存在")
            return False
        
        # 2. 检查表结构
        cursor.execute("PRAGMA table_info(timing_violations)")
        columns = cursor.fetchall()
        print("\n📋 数据库表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 3. 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 总记录数: {total_count}")
        
        if total_count == 0:
            print("❌ 数据库中没有记录")
            return False
        
        # 4. 检查NUM值的统计信息
        cursor.execute("SELECT MIN(num), MAX(num), COUNT(DISTINCT num) FROM timing_violations")
        min_num, max_num, distinct_count = cursor.fetchone()
        print(f"\n🔢 NUM值统计:")
        print(f"  最小值: {min_num}")
        print(f"  最大值: {max_num}")
        print(f"  不同值数量: {distinct_count}")
        
        # 5. 检查前20条记录的NUM值（按ID排序）
        print(f"\n📝 前20条记录的NUM值（按ID排序）:")
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY id LIMIT 20")
        rows = cursor.fetchall()
        for row in rows:
            hier_short = row[2][:50] + "..." if len(row[2]) > 50 else row[2]
            print(f"  ID={row[0]:3d}, NUM={row[1]:6d}, Hier={hier_short}")
        
        # 6. 检查前20条记录的NUM值（按NUM排序）
        print(f"\n📝 前20条记录的NUM值（按NUM排序）:")
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY num LIMIT 20")
        rows = cursor.fetchall()
        for row in rows:
            hier_short = row[2][:50] + "..." if len(row[2]) > 50 else row[2]
            print(f"  ID={row[0]:3d}, NUM={row[1]:6d}, Hier={hier_short}")
        
        # 7. 检查是否有NUM=1的记录
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num = 1")
        count_num_1 = cursor.fetchone()[0]
        print(f"\n🔍 NUM=1的记录数: {count_num_1}")
        
        if count_num_1 > 0:
            cursor.execute("SELECT id, num, hier FROM timing_violations WHERE num = 1 LIMIT 5")
            rows = cursor.fetchall()
            print("  NUM=1的记录:")
            for row in rows:
                hier_short = row[2][:50] + "..." if len(row[2]) > 50 else row[2]
                print(f"    ID={row[0]:3d}, NUM={row[1]:6d}, Hier={hier_short}")
        
        # 8. 检查NUM值在1-10范围内的记录
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num BETWEEN 1 AND 10")
        count_1_to_10 = cursor.fetchone()[0]
        print(f"\n🔍 NUM值在1-10范围内的记录数: {count_1_to_10}")
        
        if count_1_to_10 > 0:
            cursor.execute("SELECT id, num, hier FROM timing_violations WHERE num BETWEEN 1 AND 10 ORDER BY num")
            rows = cursor.fetchall()
            print("  NUM值1-10的记录:")
            for row in rows:
                hier_short = row[2][:50] + "..." if len(row[2]) > 50 else row[2]
                print(f"    ID={row[0]:3d}, NUM={row[1]:6d}, Hier={hier_short}")
        
        # 9. 检查用例信息
        cursor.execute("SELECT DISTINCT case_name, corner FROM timing_violations")
        cases = cursor.fetchall()
        print(f"\n📁 用例信息:")
        for case_name, corner in cases:
            cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE case_name = ? AND corner = ?", (case_name, corner))
            count = cursor.fetchone()[0]
            print(f"  用例: {case_name}, Corner: {corner}, 记录数: {count}")
            
            # 检查这个用例的NUM值范围
            cursor.execute("SELECT MIN(num), MAX(num) FROM timing_violations WHERE case_name = ? AND corner = ?", (case_name, corner))
            min_num, max_num = cursor.fetchone()
            print(f"    NUM范围: {min_num} - {max_num}")
        
        # 10. 检查是否有重复的NUM值
        cursor.execute("""
            SELECT num, COUNT(*) as count 
            FROM timing_violations 
            GROUP BY num 
            HAVING COUNT(*) > 1 
            ORDER BY count DESC 
            LIMIT 10
        """)
        duplicates = cursor.fetchall()
        if duplicates:
            print(f"\n⚠️  重复的NUM值:")
            for num, count in duplicates:
                print(f"  NUM={num} 出现 {count} 次")
        else:
            print(f"\n✅ 没有重复的NUM值")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_data_model():
    """通过数据模型检查数据"""
    try:
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        sys.path.insert(0, project_root)
        
        from plugins.user.timing_violation.models import ViolationDataModel
        
        print("\n" + "="*60)
        print("通过数据模型检查数据")
        print("="*60)
        
        model = ViolationDataModel()
        
        # 获取用例列表
        cases = model.get_case_list()
        print(f"📁 用例列表: {cases}")
        
        if not cases:
            print("❌ 没有找到用例")
            return False
        
        # 检查第一个用例的数据
        case_name = cases[0]
        print(f"\n🔍 检查用例: {case_name}")
        
        violations = model.get_violations(case_name)
        print(f"📊 违例记录数: {len(violations)}")
        
        if violations:
            print(f"\n📝 前10条记录:")
            for i, violation in enumerate(violations[:10]):
                num_value = violation.get('num', 'N/A')
                id_value = violation.get('id', 'N/A')
                hier = violation.get('hier', 'N/A')[:50]
                print(f"  {i+1:2d}. NUM={num_value:6}, ID={id_value:3}, Hier={hier}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 通过数据模型检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("数据库NUM值检查工具")
    print("="*60)
    
    # 检查数据库
    success1 = check_database()
    
    # 通过数据模型检查
    success2 = check_data_model()
    
    print("\n" + "="*60)
    if success1 and success2:
        print("✅ 检查完成")
    else:
        print("❌ 检查过程中出现问题")
    
    print("\n💡 如果NUM值不是从1开始，可能的原因:")
    print("1. 解析时出现问题")
    print("2. 数据库中存储了多次解析的结果")
    print("3. vio_summary.log文件被多次导入")
    print("4. 数据库中有旧数据残留")

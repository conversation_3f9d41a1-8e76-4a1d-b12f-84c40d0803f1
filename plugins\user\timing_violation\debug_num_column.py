#!/usr/bin/env python3
"""
调试NUM列显示问题的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

def debug_violation_data():
    """调试违例数据结构"""
    try:
        from plugins.user.timing_violation.models import ViolationDataModel
        
        print("创建数据模型...")
        model = ViolationDataModel()
        
        # 获取所有用例
        print("获取用例列表...")
        cases = model.get_case_list()
        print(f"找到 {len(cases)} 个用例: {cases}")
        
        if not cases:
            print("没有找到任何用例数据")
            return
        
        # 获取第一个用例的数据
        case_name = cases[0]
        print(f"\n获取用例 '{case_name}' 的数据...")
        
        violations = model.get_violations(case_name)
        print(f"找到 {len(violations)} 条违例记录")
        
        if violations:
            print("\n检查前5条记录的数据结构:")
            for i, violation in enumerate(violations[:5]):
                print(f"\n记录 {i+1}:")
                print(f"  所有字段: {list(violation.keys())}")
                print(f"  num字段: {violation.get('num', 'NOT_FOUND')}")
                print(f"  id字段: {violation.get('id', 'NOT_FOUND')}")
                print(f"  hier字段: {violation.get('hier', 'NOT_FOUND')[:50]}...")
                
                # 测试NUM列显示逻辑
                num_value = violation.get('num', '')
                if num_value is None or num_value == '':
                    num_value = violation.get('id', '')
                display_num = str(num_value) if num_value else ''
                print(f"  显示的NUM: '{display_num}'")
        
        print("\n调试完成")
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_cell_label_logic():
    """测试create_cell_label的逻辑"""
    print("\n测试create_cell_label逻辑:")
    
    # 模拟不同的violation数据
    test_cases = [
        {"num": 123, "id": 456, "hier": "test.path"},
        {"num": None, "id": 456, "hier": "test.path"},
        {"num": "", "id": 456, "hier": "test.path"},
        {"id": 456, "hier": "test.path"},  # 没有num字段
        {"hier": "test.path"},  # 既没有num也没有id
    ]
    
    for i, violation in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {violation}")
        
        # 模拟create_cell_label中的NUM列逻辑
        if True:  # col == 0 (NUM列)
            num_value = violation.get('num', '')
            if num_value is None or num_value == '':
                num_value = violation.get('id', '')
            text = str(num_value) if num_value else ''
            print(f"  结果: '{text}'")

if __name__ == "__main__":
    print("NUM列显示问题调试")
    print("=" * 50)
    
    # 测试逻辑
    test_create_cell_label_logic()
    
    # 调试实际数据
    debug_violation_data()

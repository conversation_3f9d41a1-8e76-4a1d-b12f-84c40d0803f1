#!/usr/bin/env python3
"""
调试NUM数据的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

def debug_violation_data():
    """调试违例数据"""
    try:
        from plugins.user.timing_violation.models import ViolationDataModel
        
        print("创建数据模型...")
        model = ViolationDataModel()
        
        # 获取所有用例
        print("获取用例列表...")
        cases = model.get_case_list()
        print(f"找到 {len(cases)} 个用例: {cases}")
        
        if not cases:
            print("没有找到任何用例数据")
            return
        
        # 获取第一个用例的数据
        case_name = cases[0]
        print(f"\n获取用例 '{case_name}' 的数据...")
        
        violations = model.get_violations(case_name)
        print(f"找到 {len(violations)} 条违例记录")
        
        if violations:
            print("\n检查前10条记录的NUM值:")
            for i, violation in enumerate(violations[:10]):
                num_value = violation.get('num', 'NOT_FOUND')
                id_value = violation.get('id', 'NOT_FOUND')
                hier = violation.get('hier', 'NOT_FOUND')[:50]
                print(f"记录 {i+1}: NUM={num_value}, ID={id_value}, Hier={hier}...")
            
            print(f"\n检查最后10条记录的NUM值:")
            for i, violation in enumerate(violations[-10:]):
                num_value = violation.get('num', 'NOT_FOUND')
                id_value = violation.get('id', 'NOT_FOUND')
                hier = violation.get('hier', 'NOT_FOUND')[:50]
                actual_index = len(violations) - 10 + i + 1
                print(f"记录 {actual_index}: NUM={num_value}, ID={id_value}, Hier={hier}...")
        
        print("\n调试完成")
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_directly():
    """直接检查数据库"""
    try:
        import sqlite3
        import os
        
        # 数据库路径
        db_path = os.path.join(os.getcwd(), "VIOLATION_CHECK", "timing_violations.db")
        print(f"检查数据库: {db_path}")
        
        if not os.path.exists(db_path):
            print("数据库文件不存在")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(timing_violations)")
        columns = cursor.fetchall()
        print("\n数据库表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
        
        # 检查前10条记录的NUM值
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY id LIMIT 10")
        rows = cursor.fetchall()
        print(f"\n前10条记录的NUM值:")
        for row in rows:
            print(f"  ID={row[0]}, NUM={row[1]}, Hier={row[2][:50]}...")
        
        # 检查NUM值的范围
        cursor.execute("SELECT MIN(num), MAX(num), COUNT(*) FROM timing_violations")
        min_num, max_num, count = cursor.fetchone()
        print(f"\nNUM值统计: 最小={min_num}, 最大={max_num}, 总数={count}")
        
        # 检查是否有从1开始的NUM值
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num BETWEEN 1 AND 10")
        count_1_to_10 = cursor.fetchone()[0]
        print(f"NUM值在1-10范围内的记录数: {count_1_to_10}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_display_logic():
    """模拟显示逻辑"""
    print("\n模拟显示逻辑:")
    
    # 模拟一些违例数据
    test_violations = [
        {'num': 880887, 'id': 1, 'hier': 'test.path1'},
        {'num': 880888, 'id': 2, 'hier': 'test.path2'},
        {'num': 880889, 'id': 3, 'hier': 'test.path3'},
        {'num': 1, 'id': 4, 'hier': 'test.path4'},
        {'num': 2, 'id': 5, 'hier': 'test.path5'},
    ]
    
    print("测试违例数据:")
    for i, violation in enumerate(test_violations):
        # 模拟create_cell_label的逻辑
        num_value = violation.get('num', '')
        if num_value is None or num_value == '':
            num_value = violation.get('id', '')
        display_text = str(num_value) if num_value else ''
        
        print(f"  违例 {i+1}: 原始NUM={violation['num']}, 显示='{display_text}'")

if __name__ == "__main__":
    print("NUM数据调试")
    print("=" * 60)
    
    # 检查数据库
    print("1. 直接检查数据库:")
    check_database_directly()
    
    # 检查数据模型
    print("\n2. 通过数据模型检查:")
    debug_violation_data()
    
    # 模拟显示逻辑
    print("\n3. 模拟显示逻辑:")
    simulate_display_logic()
    
    print("\n" + "=" * 60)
    print("调试完成！")
    print("\n可能的问题:")
    print("1. 数据库中的NUM值本身就不是从1开始")
    print("2. 数据加载时的排序问题")
    print("3. 分页数据获取的问题")

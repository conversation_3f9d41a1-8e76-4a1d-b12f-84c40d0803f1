#!/usr/bin/env python3
"""
调试NUM vs ID的脚本
"""

import sqlite3
import os

def check_num_vs_id():
    """检查数据库中NUM和ID的值"""
    
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    print(f"检查数据库: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查前10条记录的ID和NUM值
        print("\n📝 前10条记录的ID和NUM值:")
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY id LIMIT 10")
        rows = cursor.fetchall()
        
        for row in rows:
            id_val, num_val, hier = row
            hier_short = hier[:50] + "..." if len(hier) > 50 else hier
            print(f"  ID={id_val:7d}, NUM={num_val if num_val is not None else 'NULL':>6}, Hier={hier_short}")
        
        # 检查NUM字段的统计
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num IS NULL")
        null_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num IS NOT NULL")
        not_null_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num = 0")
        zero_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num = ''")
        empty_count = cursor.fetchone()[0]
        
        print(f"\n📊 NUM字段统计:")
        print(f"  NULL值数量: {null_count}")
        print(f"  非NULL值数量: {not_null_count}")
        print(f"  值为0的数量: {zero_count}")
        print(f"  值为空字符串的数量: {empty_count}")
        
        # 检查NUM值的范围（非NULL的）
        cursor.execute("SELECT MIN(num), MAX(num) FROM timing_violations WHERE num IS NOT NULL")
        result = cursor.fetchone()
        if result[0] is not None:
            min_num, max_num = result
            print(f"  NUM值范围（非NULL）: {min_num} - {max_num}")
        else:
            print("  没有非NULL的NUM值")
        
        # 检查ID值的范围
        cursor.execute("SELECT MIN(id), MAX(id) FROM timing_violations")
        min_id, max_id = cursor.fetchone()
        print(f"  ID值范围: {min_id} - {max_id}")
        
        # 模拟代码逻辑
        print(f"\n🔍 模拟代码逻辑:")
        cursor.execute("SELECT id, num FROM timing_violations ORDER BY id LIMIT 5")
        rows = cursor.fetchall()
        
        for row in rows:
            id_val, num_val = row
            
            # 模拟create_cell_label的逻辑
            num_value = num_val if num_val is not None else ''
            if num_value is None or num_value == '':
                num_value = id_val  # 使用ID作为回退
            
            display_text = str(num_value) if num_value else ''
            
            print(f"  ID={id_val}, NUM={num_val}, 显示值='{display_text}'")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("NUM vs ID 调试工具")
    print("="*50)
    check_num_vs_id()
    
    print("\n💡 如果NUM字段为NULL或空，代码会使用ID值作为回退")
    print("这就是为什么看到1000887这样的大数值的原因")

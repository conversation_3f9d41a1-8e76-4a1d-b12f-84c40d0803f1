#!/usr/bin/env python3
"""
修复数据库中NUM字段的脚本
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """备份数据库"""
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join("VIOLATION_CHECK", f"timing_violations_backup_{timestamp}.db")
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def check_num_field():
    """检查NUM字段的状态"""
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total = cursor.fetchone()[0]
        print(f"📊 总记录数: {total}")
        
        # 检查NUM字段的各种情况
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num IS NULL")
        null_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num = 0")
        zero_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num > 0")
        positive_count = cursor.fetchone()[0]
        
        print(f"📈 NUM字段统计:")
        print(f"  NULL值: {null_count}")
        print(f"  零值: {zero_count}")
        print(f"  正值: {positive_count}")
        
        # 检查前10条记录
        print(f"\n📝 前10条记录:")
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY id LIMIT 10")
        rows = cursor.fetchall()
        for row in rows:
            id_val, num_val, hier = row
            hier_short = hier[:30] + "..." if len(hier) > 30 else hier
            print(f"  ID={id_val:6d}, NUM={str(num_val):>6}, Hier={hier_short}")
        
        # 如果有正值，检查范围
        if positive_count > 0:
            cursor.execute("SELECT MIN(num), MAX(num) FROM timing_violations WHERE num > 0")
            min_num, max_num = cursor.fetchone()
            print(f"\n🔢 NUM值范围: {min_num} - {max_num}")
        
        conn.close()
        return {
            'total': total,
            'null_count': null_count,
            'zero_count': zero_count,
            'positive_count': positive_count
        }
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def fix_num_field():
    """修复NUM字段"""
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 方案1: 如果NUM字段都是0或NULL，按ID顺序重新编号
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num > 0")
        valid_num_count = cursor.fetchone()[0]
        
        if valid_num_count == 0:
            print("🔧 NUM字段无有效值，按ID顺序重新编号...")
            
            # 获取所有记录按ID排序
            cursor.execute("SELECT id FROM timing_violations ORDER BY id")
            ids = cursor.fetchall()
            
            # 重新编号
            for i, (record_id,) in enumerate(ids, 1):
                cursor.execute("UPDATE timing_violations SET num = ? WHERE id = ?", (i, record_id))
            
            conn.commit()
            print(f"✅ 已重新编号 {len(ids)} 条记录")
            
        else:
            print(f"📊 发现 {valid_num_count} 条有效NUM值的记录")
            
            # 检查是否有NULL或0值需要修复
            cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num IS NULL OR num = 0")
            invalid_count = cursor.fetchone()[0]
            
            if invalid_count > 0:
                print(f"🔧 修复 {invalid_count} 条无效NUM值...")
                
                # 获取最大的有效NUM值
                cursor.execute("SELECT MAX(num) FROM timing_violations WHERE num > 0")
                max_num = cursor.fetchone()[0] or 0
                
                # 获取需要修复的记录
                cursor.execute("SELECT id FROM timing_violations WHERE num IS NULL OR num = 0 ORDER BY id")
                invalid_ids = cursor.fetchall()
                
                # 从最大值+1开始编号
                for i, (record_id,) in enumerate(invalid_ids, max_num + 1):
                    cursor.execute("UPDATE timing_violations SET num = ? WHERE id = ?", (i, record_id))
                
                conn.commit()
                print(f"✅ 已修复 {len(invalid_ids)} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("NUM字段修复工具")
    print("="*50)
    
    # 1. 检查当前状态
    print("1. 检查当前NUM字段状态...")
    status = check_num_field()
    
    if not status:
        print("❌ 无法检查数据库状态")
        return
    
    # 2. 判断是否需要修复
    if status['positive_count'] > 0 and status['null_count'] == 0 and status['zero_count'] == 0:
        print("✅ NUM字段状态正常，无需修复")
        return
    
    print(f"\n⚠️  发现问题:")
    if status['null_count'] > 0:
        print(f"  - {status['null_count']} 条记录的NUM为NULL")
    if status['zero_count'] > 0:
        print(f"  - {status['zero_count']} 条记录的NUM为0")
    
    # 3. 备份数据库
    print(f"\n2. 备份数据库...")
    if not backup_database():
        print("❌ 备份失败，停止修复")
        return
    
    # 4. 修复NUM字段
    print(f"\n3. 修复NUM字段...")
    if fix_num_field():
        print(f"\n4. 验证修复结果...")
        new_status = check_num_field()
        
        if new_status and new_status['positive_count'] == new_status['total']:
            print("🎉 NUM字段修复成功！")
            print("💡 请重新启动插件查看效果")
        else:
            print("❌ 修复后仍有问题")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()

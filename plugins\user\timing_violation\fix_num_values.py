#!/usr/bin/env python3
"""
修复NUM值问题的脚本
"""

import sqlite3
import os
import sys
from datetime import datetime

def backup_database():
    """备份当前数据库"""
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    if not os.path.exists(db_path):
        print("数据库文件不存在，无需备份")
        return True
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join("VIOLATION_CHECK", f"timing_violations_backup_{timestamp}.db")
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def check_current_data():
    """检查当前数据库中的NUM值"""
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total = cursor.fetchone()[0]
        
        # 检查NUM值范围
        cursor.execute("SELECT MIN(num), MAX(num) FROM timing_violations")
        min_num, max_num = cursor.fetchone()
        
        # 检查前5条记录
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY num LIMIT 5")
        first_records = cursor.fetchall()
        
        conn.close()
        
        print(f"📊 当前数据库状态:")
        print(f"  总记录数: {total}")
        print(f"  NUM值范围: {min_num} - {max_num}")
        print(f"  前5条记录的NUM值: {[r[1] for r in first_records]}")
        
        return {
            'total': total,
            'min_num': min_num,
            'max_num': max_num,
            'first_nums': [r[1] for r in first_records]
        }
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return None

def clear_database():
    """清空数据库中的违例数据"""
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 删除所有违例记录
        cursor.execute("DELETE FROM timing_violations")
        cursor.execute("DELETE FROM confirmation_records")
        
        # 重置自增ID
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='timing_violations'")
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='confirmation_records'")
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库已清空")
        return True
        
    except Exception as e:
        print(f"❌ 清空数据库失败: {e}")
        return False

def reimport_data():
    """重新导入数据"""
    try:
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        sys.path.insert(0, project_root)
        
        from plugins.user.timing_violation.models import ViolationDataModel
        from plugins.user.timing_violation.parser import ViolationParser
        
        print("🔄 开始重新导入数据...")
        
        # 查找vio_summary.log文件
        log_files = []
        for root, dirs, files in os.walk("."):
            for file in files:
                if file == "vio_summary.log":
                    log_files.append(os.path.join(root, file))
        
        if not log_files:
            print("❌ 未找到vio_summary.log文件")
            return False
        
        print(f"📁 找到日志文件: {log_files}")
        
        # 使用第一个找到的文件
        log_file = log_files[0]
        print(f"📖 解析文件: {log_file}")
        
        # 解析文件
        parser = ViolationParser()
        violations = parser.parse_file(log_file)
        
        if not violations:
            print("❌ 解析结果为空")
            return False
        
        print(f"📊 解析到 {len(violations)} 条违例记录")
        print(f"🔢 前5条记录的NUM值: {[v['NUM'] for v in violations[:5]]}")
        
        # 导入到数据库
        model = ViolationDataModel()
        case_name = os.path.basename(os.path.dirname(log_file))
        corner = "default"
        
        success_count = model.add_violations(case_name, corner, violations, log_file)
        print(f"✅ 成功导入 {success_count} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 重新导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("NUM值修复工具")
    print("="*50)
    
    # 1. 检查当前数据
    print("1. 检查当前数据库状态...")
    current_data = check_current_data()
    
    if current_data and current_data['min_num'] == 1:
        print("✅ NUM值已经从1开始，无需修复")
        return
    
    # 2. 备份数据库
    print("\n2. 备份当前数据库...")
    if not backup_database():
        print("❌ 备份失败，停止操作")
        return
    
    # 3. 清空数据库
    print("\n3. 清空数据库...")
    if not clear_database():
        print("❌ 清空失败，停止操作")
        return
    
    # 4. 重新导入数据
    print("\n4. 重新导入数据...")
    if not reimport_data():
        print("❌ 重新导入失败")
        return
    
    # 5. 验证结果
    print("\n5. 验证修复结果...")
    new_data = check_current_data()
    
    if new_data and new_data['min_num'] == 1:
        print("🎉 NUM值修复成功！现在从1开始")
    else:
        print("❌ 修复失败，NUM值仍然不正确")

if __name__ == "__main__":
    main()

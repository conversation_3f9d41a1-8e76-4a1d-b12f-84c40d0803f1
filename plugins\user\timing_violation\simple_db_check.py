#!/usr/bin/env python3
"""
简单的数据库检查脚本
"""

import sqlite3
import os

def main():
    # 数据库路径
    db_path = os.path.join("VIOLATION_CHECK", "timing_violations.db")
    print(f"检查数据库: {db_path}")
    
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total = cursor.fetchone()[0]
        print(f"总记录数: {total}")
        
        # 检查NUM值范围
        cursor.execute("SELECT MIN(num), MAX(num) FROM timing_violations")
        min_num, max_num = cursor.fetchone()
        print(f"NUM值范围: {min_num} - {max_num}")
        
        # 检查前10条记录
        print("\n前10条记录（按NUM排序）:")
        cursor.execute("SELECT id, num, hier FROM timing_violations ORDER BY num LIMIT 10")
        rows = cursor.fetchall()
        for row in rows:
            print(f"ID={row[0]}, NUM={row[1]}, Hier={row[2][:50]}...")
        
        # 检查是否有NUM=1的记录
        cursor.execute("SELECT COUNT(*) FROM timing_violations WHERE num = 1")
        count_1 = cursor.fetchone()[0]
        print(f"\nNUM=1的记录数: {count_1}")
        
        # 检查NUM值1-10的记录
        cursor.execute("SELECT id, num FROM timing_violations WHERE num BETWEEN 1 AND 10 ORDER BY num")
        rows = cursor.fetchall()
        if rows:
            print("NUM值1-10的记录:")
            for row in rows:
                print(f"  ID={row[0]}, NUM={row[1]}")
        else:
            print("没有NUM值在1-10范围内的记录")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试NUM列显示修复的脚本
"""

def test_num_column_logic():
    """测试NUM列显示逻辑"""
    print("测试NUM列显示逻辑")
    print("=" * 50)
    
    # 模拟不同页面的情况
    test_cases = [
        # (页面号, 页面大小, 行索引, 期望的NUM值)
        (0, 10, 0, "1"),    # 第1页第1行
        (0, 10, 1, "2"),    # 第1页第2行
        (0, 10, 9, "10"),   # 第1页第10行
        (1, 10, 0, "1"),    # 第2页第1行
        (1, 10, 1, "2"),    # 第2页第2行
        (1, 10, 9, "10"),   # 第2页第10行
        (2, 10, 0, "1"),    # 第3页第1行
        (0, 100, 0, "1"),   # 大页面第1行
        (0, 100, 99, "100"), # 大页面第100行
        (1, 100, 0, "1"),   # 大页面第2页第1行
    ]
    
    print("测试create_cell_label中的NUM列逻辑:")
    for page, page_size, row, expected in test_cases:
        # 模拟create_cell_label中的逻辑
        # row是相对于当前页的行索引
        page_num = row + 1
        result = str(page_num)
        
        status = "✓" if result == expected else "✗"
        print(f"{status} 页面{page+1}, 行{row+1}: 期望'{expected}', 实际'{result}'")
    
    print("\n测试_get_display_data中的NUM列逻辑:")
    for page, page_size, row, expected in test_cases:
        # 模拟_get_display_data中的逻辑
        # 这里row_index是绝对行索引
        absolute_row_index = page * page_size + row
        page_row = absolute_row_index % page_size
        result = str(page_row + 1)
        
        status = "✓" if result == expected else "✗"
        print(f"{status} 页面{page+1}, 绝对行{absolute_row_index+1}: 期望'{expected}', 实际'{result}'")

def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况")
    print("=" * 50)
    
    # 测试空数据情况
    print("测试空数据情况:")
    
    # 模拟violation数据为空的情况
    violation_empty = {}
    
    # 模拟create_cell_label的逻辑
    row = 0  # 第一行
    page_num = row + 1
    result = str(page_num)
    print(f"空数据时NUM列显示: '{result}'")
    
    # 测试数据库num字段存在的情况
    print("\n测试数据库num字段存在的情况:")
    violation_with_num = {'num': 760887, 'id': 123}
    
    # 在create_cell_label中，我们不再使用数据库的num字段
    row = 0
    page_num = row + 1
    result = str(page_num)
    print(f"有num字段时NUM列显示: '{result}' (忽略数据库num={violation_with_num['num']})")

def simulate_page_navigation():
    """模拟页面导航"""
    print("\n模拟页面导航")
    print("=" * 50)
    
    # 模拟总共1000条记录，每页100条，共10页
    total_records = 1000
    page_size = 100
    total_pages = (total_records + page_size - 1) // page_size
    
    print(f"总记录数: {total_records}, 页面大小: {page_size}, 总页数: {total_pages}")
    
    # 测试每一页的第一行和最后一行
    for page in range(min(3, total_pages)):  # 只测试前3页
        print(f"\n第{page+1}页:")
        
        # 第一行
        row = 0
        page_num = row + 1
        print(f"  第一行NUM: {page_num}")
        
        # 最后一行
        last_row = min(page_size - 1, total_records - page * page_size - 1)
        page_num = last_row + 1
        print(f"  最后一行NUM: {page_num}")

if __name__ == "__main__":
    print("NUM列显示修复测试")
    print("=" * 60)
    
    test_num_column_logic()
    test_edge_cases()
    simulate_page_navigation()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n修复说明:")
    print("1. NUM列现在显示页面内的序号（1-页面大小）")
    print("2. 不再显示数据库中的原始num值")
    print("3. 每页都从1开始计数")
    print("4. 保持了页面间的一致性")

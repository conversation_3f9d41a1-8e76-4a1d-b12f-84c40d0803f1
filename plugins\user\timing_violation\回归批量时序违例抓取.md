我想为时序违例插件（位于 `e:\doc\python\runsim/plugins\user\timing_violation/`）增加回归目录的 vio_summary.log 批量抓取功能。具体需求如下：

**功能概述：**
在现有时序违例检查框架基础上，新增回归目录扫描功能，支持批量收集和分析多个回归用例的时序违例日志。

**目录结构规范：**
- 默认回归根路径：`./regression`（用户可配置修改）
- 回归目录层级结构：`./regression/<subsys>/.../<case_name>_<corner_name>/<case_name>_<seed_number>/log/vio_summary.log`
- 其中：
- `<subsys>`：子系统名称，通常以 `_sys` 结尾或为 `top` 目录
- ...：中间目录层次
- `<corner_name>`：工艺角信息
- `<case_name>`：测试用例名称
- `<seed_number>`：随机种子号

**分类层级要求：**
1. 一级分类：按子系统（subsys）分组
2. 二级分类：按工艺角（corner_name）分组
3. 三级分类：按用例名（case_name）分组
4. 四级分类：按种子号（seed）分组

**功能特性：**
1. **兼容性**：与现有时序违例检查框架完全兼容，不影响原有work目录下单个vio_summary.log的分析功能
2. **多选支持**：支持用户选择多个vio_summary.log文件进行批量分析
3. **分页显示**：分析结果按 `<corner_name>_<case_name>_<seed>` 格式进行分页表格显示
4. **用户交互**：提供友好的界面供用户排查时序违例问题并进行确认操作

**技术实现考虑：**
- 需要递归扫描回归目录结构
- 解析目录名提取subsys、corner_name、case_name、seed信息
- 集成到现有GUI界面中
- 需要考虑大数据量性能问题
- 保持与现有代码架构的一致性
- 考虑到回归用例很多，所有violation集中在一起（十万+违例） 可能会导致性能问题 ，可以先形成一份

请基于以上需求，分析现有时序违例插件的代码结构，并提供详细的开发方案和实现步骤。 

# 复位区间功能使用示例

## 功能介绍

在时序违例确认插件中，新增了"复位区间"输入框，位于原有"复位时间"输入框的右侧。该功能允许用户指定一个时间区间，系统会自动确认该区间内的所有时序违例。

## 界面布局

```
[用例名称: ________] [Corner: _______] [复位时间(ns): 1000] [复位区间(ns): 5000~6000]
```

## 使用场景

### 场景1：仅使用复位时间（原有功能）
**适用情况**：需要确认复位前的所有违例

**操作步骤**：
1. 在"复位时间(ns)"输入框中输入：`1000`
2. "复位区间(ns)"输入框保持空白
3. 点击"自动确认"按钮

**确认结果**：系统会确认所有时间 ≤ 1000ns 的违例

### 场景2：仅使用复位区间（新功能）
**适用情况**：需要确认特定时间段内的违例

**操作步骤**：
1. "复位时间(ns)"输入框可以保持原值或清空
2. 在"复位区间(ns)"输入框中输入：`5000~6000`
3. 点击"自动确认"按钮

**确认结果**：系统会确认所有时间在 5000ns~6000ns 区间内的违例

### 场景3：同时使用两种条件（组合功能）
**适用情况**：需要确认复位前和特定区间内的违例

**操作步骤**：
1. 在"复位时间(ns)"输入框中输入：`1000`
2. 在"复位区间(ns)"输入框中输入：`5000~6000`
3. 点击"自动确认"按钮

**确认结果**：系统会确认满足以下任一条件的违例：
- 时间 ≤ 1000ns（复位前）
- 时间在 5000ns~6000ns 区间内

## 输入格式说明

### 复位区间输入格式
- **正确格式**：`开始时间~结束时间`
- **示例**：
  - `5000~6000`（整数）
  - `1000.5~2000.5`（小数）
  - `0~1000`（从0开始）

### 输入验证规则
1. **格式要求**：必须包含一个`~`符号分隔开始和结束时间
2. **数值要求**：开始时间和结束时间都必须是非负数
3. **逻辑要求**：开始时间必须小于结束时间
4. **可选性**：复位区间输入框可以为空

### 错误示例及提示
| 错误输入 | 错误提示 |
|---------|---------|
| `5000` | 复位区间格式错误，请使用格式：开始时间~结束时间 |
| `5000~` | 复位区间格式错误，请使用格式：开始时间~结束时间 |
| `~6000` | 复位区间格式错误，请使用格式：开始时间~结束时间 |
| `abc~6000` | 时间值格式错误，请输入数字 |
| `-1000~6000` | 时间值不能为负数 |
| `6000~5000` | 开始时间必须小于结束时间 |

## 实际使用示例

### 示例1：芯片复位序列分析
**背景**：某芯片的复位序列在1000ns完成，但在5000ns~6000ns期间有特殊的时钟切换操作

**设置**：
- 复位时间：`1000`
- 复位区间：`5000~6000`

**结果**：系统会自动确认复位期间（≤1000ns）和时钟切换期间（5000ns~6000ns）的所有时序违例

### 示例2：多阶段复位处理
**背景**：系统有多个复位阶段，需要分别处理不同时间段的违例

**第一次确认**：
- 复位时间：`500`（第一阶段复位）
- 复位区间：留空

**第二次确认**：
- 复位时间：留空
- 复位区间：`2000~3000`（第二阶段复位）

### 示例3：精确时间窗口确认
**背景**：只需要确认特定时间窗口内的违例，不涉及传统的复位时间

**设置**：
- 复位时间：留空或保持默认值
- 复位区间：`8500~9500`

**结果**：只确认8500ns~9500ns时间窗口内的违例

## 注意事项

1. **至少一个条件**：复位时间和复位区间至少要填写一个，否则系统会提示错误
2. **OR逻辑**：当同时设置两个条件时，系统使用OR逻辑，满足任一条件的违例都会被确认
3. **时间单位**：所有时间输入都使用纳秒(ns)作为单位
4. **确认理由**：系统会自动生成详细的确认理由，说明是基于哪个条件进行的确认

## 技术说明

- **数据库查询**：系统会在数据库中查找满足时间条件且状态为"待确认"的违例记录
- **批量处理**：支持批量确认多条违例记录
- **历史记录**：所有自动确认的操作都会记录在数据库中，包含详细的确认理由
- **兼容性**：新功能完全兼容原有的复位时间功能，不影响现有工作流程
